<script setup>
import { computed, getCurrentInstance, inject, onMounted, ref } from 'vue'
import GenericForm from '@/components/GenericForm.vue'
import ApplicationApi from '@/api/llm/application.js'
import DatasetApi from '@/api/llm/dataset.js'
import UtilApi from '@/api/util.js'

const { proxy } = getCurrentInstance()

const _config = UtilApi.validation('com.chinamobile.si.dubhe.model.DatasetApplication', ['name', 'description', 'prompt'])

// 表单字段定义
const fields = ref([{
  title: '应用名称',
  field: 'name',
  type: 'text',
  config: {
    promise: _config.name
  }
}, {
  title: '描述',
  field: 'description',
  type: 'textarea',
  config: {
    promise: _config.description
  }
}, {
  title: '系统提示词',
  field: 'prompt',
  type: 'textarea',
  config: {
    promise: new Promise(resolve => {
      _config.prompt.then(data => {
        data.rows = 10
        resolve(data)
      })
    })
  }
}, {
  title: '关联知识库',
  field: 'datasets',
  type: 'select',
  config: {
    promise: new Promise(resolve => {
      DatasetApi.searchUserDatasets(null, {
        toast: {
          success: false
        }
      }).then(result => {
        const _options = {
          mode: 'multiple',
          options: result.data.map(i => ({
            label: i.title,
            value: i.id
          }))
        }

        resolve(_options)
      })
    })
  }
}])

const reloadPage = inject('reloadPage')
const closePage = inject('closePage')

const actions = ref([{
  title: '保存',
  callback (record) {
    const _promise = ApplicationApi.saveDataset(record)
    _promise.then(result => {
      if (record.id == null) {
        proxy.$router.replace({
          query: {
            id: result.data
          }
        })
      } else {
        reloadPage()
      }
    })

    return _promise
  }
}, {
  title: '删除',
  callback (record) {
    const _promise = ApplicationApi.remove(record.id)

    _promise.then(() => {
      closePage(proxy.$route.path)
    })

    return _promise
  }
}])

const applicationId = computed(() => proxy.$route.query.id)

const form = ref()

onMounted(() => {
  if (applicationId.value) {
    ApplicationApi.get(applicationId.value, {
      showLoading: false,
      toast: {
        success: false
      }
    })
      .then(result => {
        form.value.setModel(result.data)
      })
      .catch(() => {
        actions.value = []
      })
  } else {
    form.value.setModel({
      datasets: []
    })

    // 新增时移除删除按钮
    actions.value.splice(1, 1)
  }
})
</script>

<template>
  <a-card :title="'知识库对话应用'">
    <a-row :gutter="24">
      <a-col :span="8">
        <a-card
          :size="'small'"
          :title="'说明'"
        >
          <div class="help-content">
            <p>基于知识库文档进行智能问答的应用类型。</p>

            <h5>功能特点：</h5>
            <ul>
              <li>基于知识库内容回答问题</li>
              <li>支持文档检索和语义搜索</li>
              <li>提供准确的信息来源引用</li>
              <li>适合客服、文档问答等场景</li>
            </ul>

            <h5>使用说明：</h5>
            <ol>
              <li>设置应用名称和描述</li>
              <li>选择要关联的知识库</li>
              <li>保存后即可开始使用</li>
            </ol>
          </div>
        </a-card>
      </a-col>
      <a-col :span="16">
        <GenericForm
          ref="form"
          :layout="{
            showLabel: true,
            labelCol: {
              sm: {
                span: 7
              },
              lg: {
                span: 4
              }
            },
            wrapperCol: {
              sm: {
                span: 17
              },
              lg: {
                span: 20
              }
            }
          }"
          :fields="fields"
          :actions="actions"
        />
      </a-col>
    </a-row>
  </a-card>
</template>

<style lang="less" scoped>
.help-content {
  h5 {
    color: #595959;
    margin: 16px 0 8px 0;
    font-size: 13px;
  }

  p {
    color: #8c8c8c;
    margin-bottom: 12px;
    line-height: 1.5;
  }

  ul, ol {
    color: #8c8c8c;
    font-size: 13px;
    line-height: 1.5;

    li {
      margin-bottom: 4px;
    }
  }
}
</style>
